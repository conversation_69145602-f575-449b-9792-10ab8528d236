<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Podcast Player</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #1f1f1f;
            width: 428px;
            height: 926px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .player-container {
            width: 100%;
            height: 100%;
            padding: 0 32px;
            display: flex;
            flex-direction: column;
        }

        /* Header */
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 58px 0 32px 0;
            height: 106px;
        }

        .back-button {
            width: 48px;
            height: 48px;
            opacity: 0.1;
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f1f1f;
        }

        .menu-dots {
            width: 24px;
            height: 24px;
            opacity: 0.5;
        }

        /* Album Cover */
        .album-cover-container {
            width: 364px;
            height: 364px;
            margin: 0 auto 32px auto;
            position: relative;
        }

        .album-cover {
            width: 100%;
            height: 100%;
            border-radius: 24px;
            object-fit: cover;
        }

        /* Action Buttons */
        .action-buttons {
            width: 266px;
            height: 72px;
            margin: 0 auto 32px auto;
            background: rgba(76, 0, 153, 0.1);
            border-radius: 48px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 0 20px;
        }

        .action-button {
            width: 32px;
            height: 32px;
            opacity: 0.5;
        }

        /* Track Info */
        .track-info {
            text-align: center;
            margin-bottom: 40px;
        }

        .track-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f1f1f;
            margin-bottom: 10px;
        }

        .track-category {
            font-size: 16px;
            color: #1f1f1f;
            opacity: 0.7;
        }

        /* Progress Section */
        .progress-section {
            margin-bottom: 40px;
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            margin-bottom: 24px;
            font-size: 16px;
            color: #1f1f1f;
            opacity: 0.7;
        }

        /* Waveform */
        .waveform {
            width: 364px;
            height: 60px;
            margin: 0 auto;
            display: flex;
            align-items: end;
            gap: 6px;
            padding: 0 2px;
        }

        .wave-bar {
            width: 5.74px;
            background: #4c0099;
            border-radius: 1px;
        }

        .wave-bar.inactive {
            background: rgba(31, 31, 31, 0.2);
        }

        /* Control Buttons */
        .control-buttons {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 40px;
            padding: 0 20px;
        }

        .control-button {
            width: 32px;
            height: 32px;
            opacity: 0.7;
        }

        .play-button-container {
            width: 80px;
            height: 80px;
            background: #4c0099;
            border-radius: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .play-button {
            width: 32px;
            height: 32px;
        }
    </style>
</head>
<body>
    <div class="player-container">
        <!-- Header -->
        <div class="header">
            <img src="images/back-button.png" alt="Back" class="back-button">
            <div class="header-title">Now Playing</div>
            <img src="images/menu-dots.png" alt="Menu" class="menu-dots">
        </div>

        <!-- Album Cover -->
        <div class="album-cover-container">
            <img src="images/podcast-cover.png" alt="Podcast Cover" class="album-cover">
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <img src="images/share-icon.png" alt="Share" class="action-button">
            <img src="images/heart-icon.png" alt="Like" class="action-button">
            <img src="images/archive-icon.png" alt="Archive" class="action-button">
        </div>

        <!-- Track Info -->
        <div class="track-info">
            <div class="track-title">Sunday Vibes - Rift</div>
            <div class="track-category">Entertainment</div>
        </div>

        <!-- Progress Section -->
        <div class="progress-section">
            <div class="time-display">
                <span>07:00</span>
                <span>15:00</span>
            </div>
            
            <!-- Waveform -->
            <div class="waveform">
                <div class="wave-bar" style="height: 20px;"></div>
                <div class="wave-bar" style="height: 16px;"></div>
                <div class="wave-bar" style="height: 30px;"></div>
                <div class="wave-bar" style="height: 20px;"></div>
                <div class="wave-bar" style="height: 16px;"></div>
                <div class="wave-bar" style="height: 30px;"></div>
                <div class="wave-bar" style="height: 30px;"></div>
                <div class="wave-bar" style="height: 20px;"></div>
                <div class="wave-bar" style="height: 30px;"></div>
                <div class="wave-bar" style="height: 60px;"></div>
                <div class="wave-bar" style="height: 30px;"></div>
                <div class="wave-bar" style="height: 30px;"></div>
                <div class="wave-bar" style="height: 46px;"></div>
                <div class="wave-bar" style="height: 60px;"></div>
                <div class="wave-bar inactive" style="height: 26px;"></div>
                <div class="wave-bar inactive" style="height: 16px;"></div>
                <div class="wave-bar inactive" style="height: 20px;"></div>
                <div class="wave-bar inactive" style="height: 16px;"></div>
                <div class="wave-bar inactive" style="height: 30px;"></div>
                <div class="wave-bar inactive" style="height: 20px;"></div>
                <div class="wave-bar inactive" style="height: 16px;"></div>
                <div class="wave-bar inactive" style="height: 30px;"></div>
                <div class="wave-bar inactive" style="height: 30px;"></div>
                <div class="wave-bar inactive" style="height: 20px;"></div>
                <div class="wave-bar inactive" style="height: 30px;"></div>
                <div class="wave-bar inactive" style="height: 60px;"></div>
                <div class="wave-bar inactive" style="height: 30px;"></div>
                <div class="wave-bar inactive" style="height: 30px;"></div>
                <div class="wave-bar inactive" style="height: 60px;"></div>
                <div class="wave-bar inactive" style="height: 26px;"></div>
                <div class="wave-bar inactive" style="height: 16px;"></div>
            </div>
        </div>

        <!-- Control Buttons -->
        <div class="control-buttons">
            <img src="images/shuffle-icon.png" alt="Shuffle" class="control-button">
            <img src="images/rewind-icon.png" alt="Rewind" class="control-button">
            
            <div class="play-button-container">
                <img src="images/play-button.png" alt="Play" class="play-button">
            </div>
            
            <img src="images/forward-icon.png" alt="Forward" class="control-button">
            <img src="images/sort-icon.png" alt="Sort" class="control-button">
        </div>
    </div>
</body>
</html>
